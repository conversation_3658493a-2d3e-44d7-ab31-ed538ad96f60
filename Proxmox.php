<?php

namespace Paymenter\Extensions\Servers\Proxmox;

use App\Classes\Extension\Server;
use App\Models\Service;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\HtmlString;

class Proxmox extends Server
{
    // Config to connect to Proxmox API
    public function getConfig($values = []): array
    {
        return [
            [
                'name' => 'host',
                'label' => 'Proxmox API URL',
                'type' => 'text',
                'description' => 'Base URL for your Proxmox server API (e.g. https://proxmox.example.com:8006/api2/json)',
                'required' => true,
                'validation' => 'url',
            ],
            [
                'name' => 'username',
                'label' => 'API Username',
                'type' => 'text',
                'required' => true,
            ],
            [
                'name' => 'password',
                'label' => 'API Password',
                'type' => 'password',
                'required' => true,
                'encrypted' => true,
            ],
            [
                'name' => 'realm',
                'label' => 'Realm',
                'type' => 'text',
                'description' => 'Authentication realm (default: pam)',
                'required' => false,
                'default' => 'pam',
            ],
        ];
    }

    // Test Proxmox API connection
    public function testConfig(): bool|string
    {
        try {
            $this->authenticate();
            $this->request('/nodes');
        } catch (\Exception $e) {
            return $e->getMessage();
        }
        return true;
    }

    private $ticket = null;
    private $csrf_token = null;

    // Authenticate and store ticket for further requests
    private function authenticate()
    {
        if ($this->ticket) return; // already authenticated

        $response = Http::asForm()->post(rtrim($this->config('host'), '/') . '/access/ticket', [
            'username' => $this->config('username'),
            'password' => $this->config('password'),
            'realm' => $this->config('realm', 'pam'),
        ]);

        if (!$response->successful()) {
            throw new \Exception('Authentication failed: ' . $response->body());
        }

        $data = $response->json()['data'];
        $this->ticket = $data['ticket'];
        $this->csrf_token = $data['CSRFPreventionToken'];
    }

    // Make authenticated request to Proxmox API
    public function request($url, $method = 'get', $data = [])
    {
        $this->authenticate();

        $req_url = rtrim($this->config('host'), '/') . $url;

        $headers = [
            'Cookie' => 'PVEAuthCookie=' . $this->ticket,
        ];

        if (in_array(strtolower($method), ['post', 'put', 'delete'])) {
            $headers['CSRFPreventionToken'] = $this->csrf_token;
        }

        $response = Http::withHeaders($headers)->$method($req_url, $data);

        if (!$response->successful()) {
            $error = $response->json()['errors'][0]['message'] ?? $response->body();
            throw new \Exception('Proxmox API error: ' . $error);
        }

        return $response->json()['data'] ?? [];
    }

    // Product config for defining locations, IP pools, VM templates
    public function getProductConfig($values = []): array
    {
        // Example: IP pools and locations as multi-select, VM templates per location, OS options

        return [
            [
                'name' => 'locations',
                'label' => 'Available Locations',
                'type' => 'textarea',
                'description' => 'Define locations, one per line (e.g. us-east, eu-west)',
                'required' => true,
                'rows' => 3,
            ],
            [
                'name' => 'ip_pools',
                'label' => 'IP Pools',
                'type' => 'textarea',
                'description' => "Define IP Pools as JSON, keyed by location. Example:\n" .
                    "{\n\"us-east\": [\"***********0\", \"***********1\"],\n\"eu-west\": [\"*********\"]\n}",
                'required' => true,
                'validation' => 'json',
                'rows' => 5,
            ],
            [
                'name' => 'location_vm_map',
                'label' => 'Location to VM Clone Mapping',
                'type' => 'textarea',
                'description' => "Define which VM ID to clone per location and OS, JSON format. Example:\n" .
                    "{\n  \"us-east\": {\"ubuntu\": 100, \"debian\": 101},\n  \"eu-west\": {\"ubuntu\": 200}\n}",
                'required' => true,
                'validation' => 'json',
                'rows' => 5,
            ],
            [
                'name' => 'default_os',
                'label' => 'Default OS',
                'type' => 'select',
                'options' => ['ubuntu' => 'Ubuntu', 'debian' => 'Debian', 'centos' => 'CentOS'],
                'required' => true,
                'description' => 'Default OS to deploy if not specified',
            ],
        ];
    }

    // Helper to get available locations from textarea
    private function getAvailableLocations($settings)
    {
        $locationsText = $settings['locations'] ?? '';
        $locations = array_filter(array_map('trim', explode("\n", $locationsText)));
        return $locations;
    }

    // Create server by cloning the specified VM template on the correct location and assign IP from pool
    public function createServer(Service $service, $settings, $properties)
    {
        // Merge settings with properties passed
        $settings = array_merge($settings, $properties);

        $location = $settings['location'] ?? null;
        $os = $settings['os'] ?? $settings['default_os'] ?? null;

        if (!$location || !$os) {
            throw new \Exception('Location and OS must be specified.');
        }

        // Validate that the selected location is in the available locations
        $availableLocations = $this->getAvailableLocations($settings);
        if (!in_array($location, $availableLocations)) {
            throw new \Exception("Selected location '{$location}' is not in the available locations list.");
        }

        $ipPools = json_decode($settings['ip_pools'], true);
        $locationVmMap = json_decode($settings['location_vm_map'], true);

        if (!isset($ipPools[$location])) {
            throw new \Exception("No IP pool defined for location: {$location}");
        }

        if (!isset($locationVmMap[$location][$os])) {
            throw new \Exception("No VM template defined for location '{$location}' and OS '{$os}'");
        }

        $templateVmId = $locationVmMap[$location][$os];
        $availableIps = $ipPools[$location];

        if (empty($availableIps)) {
            throw new \Exception("No available IPs in the pool for location: {$location}");
        }

        // Pick the first IP from the pool for this example
        $assignedIp = array_shift($availableIps);

        // TODO: Save the updated IP pool without the assigned IP (you'll need persistent storage or DB for this)

        // Clone VM by calling Proxmox API clone endpoint
        // You should assign new VMID (usually next free ID)
        $nextVmId = $this->getNextVmId();

        $cloneData = [
            'newid' => $nextVmId,
            'name' => $service->product->name . '-' . $service->id,
        ];

        $this->request("/nodes/{$location}/qemu/{$templateVmId}/clone", 'post', $cloneData);

        // Configure VM network to assigned IP - needs to be done via cloud-init or other means
        // This is example and might depend on your template and setup
        $this->request("/nodes/{$location}/qemu/{$nextVmId}/config", 'post', [
            'ipconfig0' => "ip={$assignedIp}/24,gw=***********",
        ]);

        return [
            'vmid' => $nextVmId,
            'ip' => $assignedIp,
            'link' => $this->config('host') . "/?console=kvm&vmid={$nextVmId}",
        ];
    }

    // Helper to get next free VMID from Proxmox
    private function getNextVmId()
    {
        $usedIds = [];
        $nodes = $this->request('/nodes');
        foreach ($nodes as $node) {
            $qemus = $this->request("/nodes/{$node['node']}/qemu");
            foreach ($qemus as $vm) {
                $usedIds[] = (int)$vm['vmid'];
            }
        }

        $vmid = 100; // Start searching from 100 (default minimum VMID)
        while (in_array($vmid, $usedIds)) {
            $vmid++;
        }
        return $vmid;
    }
}